//
//  UnityView.m
//  IPAPatchFramework
//
//  Created by Subo on 21/05/2025.
//  Copyright © 2025 Weibo. All rights reserved.
//

#import "AGUnityView.h"
#import "data-Swift.h"
#import "AGConst.h"
#import "ToolInject.h"
#import "UnityTimer.h"
#import "KCUnity.h"

@interface AGUnityView ()<NPListenerDelegate>

@property (nonatomic, strong) NPListener *listener;
@property (nonatomic, assign) BOOL isEnableGoPlus;
@property(nonatomic, assign) BOOL isFakePartyManager;
@property(nonatomic, assign) BOOL isFakeTimerManager;

- (void)loadIsEnableGoPlusFromUserDefaults;
- (void)saveIsEnableGoPlusToUserDefaults;


@end

@implementation AGUnityView

+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{

        updateUserSettings();

        AGUnityView *instance = [AGUnityView sharedInstance];

        [instance InitUnityView];

        //NSLog(@"- unityView_PartyManager ");
        [instance unityView_PartyManager];

        //NSLog(@"- unityView_TimerManager ");
        [instance unityView_TimerManager];

        [instance startTCPServer];


        NSLog(@"[=====] load done!");
    });
}

+ (instancetype)sharedInstance {
    static AGUnityView *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[AGUnityView alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        [self loadIsEnableGoPlusFromUserDefaults];
    }
    return self;
}

void updateUserSettings(void) {
    NSDictionary *defaultValues = @{
        @"last_warning_time": @([[NSDate date] timeIntervalSince1970]),
        @"user_wants_ban": @(YES),
        @"last_party_time": @([[NSDate date] timeIntervalSince1970]),//15
        @"firstLaunchb": @(YES),//dscd
    };
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [defaultValues enumerateKeysAndObjectsUsingBlock:^(id key, id value, BOOL *stop) {
        [userDefaults setObject:value forKey:key];
    }];
    [userDefaults synchronize];
    NSLog(@"[=====] user setting info done! ");
}

- (void)loadIsEnableGoPlusFromUserDefaults {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    _isEnableGoPlus = [defaults boolForKey:@"isEnableGoPlus"];
    NSLog(@"[=====] loadIsEnableGoPlusFromUserDefaults isEnableGoPlus: %d ", _isEnableGoPlus);
}

- (void)saveIsEnableGoPlusToUserDefaults {
    NSLog(@"[=====] saveIsEnableGoPlusToUserDefaults isEnableGoPlus: %d ", _isEnableGoPlus);
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    [defaults setBool:_isEnableGoPlus forKey:@"isEnableGoPlus"];
    [defaults synchronize];
}

- (void)setIsEnableGoPlus:(BOOL)isEnableGoPlus {
    _isEnableGoPlus = isEnableGoPlus;
    NSLog(@"[=====] setIsEnableGoPlus isEnableGoPlus: %d ", _isEnableGoPlus);
    [[AGUnityView sharedInstance] saveIsEnableGoPlusToUserDefaults];
}

+ (NSString *)getUserSettings {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSDictionary *prefs = [defaults dictionaryForKey:@"cleanup_prefs"];
    if (!prefs) {
        NSLog(@"[=====] No found user setting!clean.");
        return nil;
    }
    NSError *error = nil;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:prefs options:NSJSONWritingPrettyPrinted error:&error];
    if (error) {
        NSLog(@"[=====] Convert user settings to js failed: %@", error);
        return nil;
    }
    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    //NSLog(@"- gen user settings success: %@", jsonString);
    return jsonString;
}

- (void) killTimer {
    NSLog(@"[=====] killTimer ");
    [[AGUnityView sharedInstance] enableGoPlus];
    //NSLog(@"- killTimer done!");
}

- (void)unityView_PartyManager {
    if (self.isFakePartyManager) {
        return;
    }
    NSLog(@"[=====] unityView_PartyManager ");

    [self enableGoPlus];
    Class class = objc_getClass("IGP_PartyManager");
    if (!class) {
        NSLog(@"[=====] not found class! unityView P");
        return;
    }

    updateUserSettings();

    NSString *userSetting = [AGUnityView getUserSettings];
    NSLog(@"[=====] CleanupPrefs: %@", userSetting);

    HookMethod(class,
                  [self class],
                  @selector(startPartyTimer),
                  @selector(killTimer));

    NSLog(@"[=====] unityView_PartyManager done! ");
    self.isFakePartyManager = YES;
}

- (void)killTimerWithName:(id)name
                                 interval:(double)interval
                                    queue:(id)queue
                                  repeats:(BOOL)repeats
                                   option:(int64_t)option
                                   action:(id)action {
    NSLog(@"[=====] killTimerWithName: %@, interval: %f, queue: %@, repeats: %d, option: %lld, action: %@", name, interval, queue, repeats, option, action);

    if ([name isEqualToString:@"SpPro.PartyTimer"]) {
        interval = 1000000.0;
        //return;
    }

    [self killTimerWithName:name interval:interval queue:queue repeats:repeats option:option action:action];
    //NSLog(@"- killTimerWithName done: %@, interval: %f, queue: %@, repeats: %d, option: %lld, action: %@", name, interval, queue, repeats, option, action);
}

- (void) unityView_TimerManager {
    if (self.isFakeTimerManager) {
        return;
    }
    NSLog(@"[=====] unityView_TimerManager ");

    Class class = objc_getClass("ZTGCDTimerManager");
    if (!class) {
        NSLog(@"[=====] not found class! unityView T");
        return;
    }

    HookMethod(class,
                  [self class],
                  @selector(scheduleGCDTimerWithName:interval:queue:repeats:option:action:),
                  @selector(killTimerWithName:interval:queue:repeats:option:action:));

    NSLog(@"[=====] unityView_TimerManager done! ");
    self.isFakeTimerManager = YES;
}

- (void)enableGoPlus {
    NSLog(@"[=====] enableGoPlus start ");

    Class settingsManagerClass = NSClassFromString(@"IGP_SettingsManager");
    if (settingsManagerClass) {
        //NSLog(@"- get Setting Manager: %d ", self.isEnableGoPlus);

        id settingsManagerInstance = [settingsManagerClass performSelector:@selector(sharedManager)];

//        [settingsManagerInstance setValue:@(self.isEnableGoPlus) forKey:@"enablePokemonCatcher"];
//        //NSLog(@"- 1 set enable catcher.");
//
//        [settingsManagerInstance setValue:@(self.isEnableGoPlus) forKey:@"enablePoiSpinner"];
//        //NSLog(@"- 1 set enable poi spinner.");
//
//        [settingsManagerInstance setValue:@(self.isEnableGoPlus) forKey:@"enableBerryFeed"];
//        //NSLog(@"- 1 set enable berry feed.");
//
//        [settingsManagerInstance setValue:@(self.isEnableGoPlus) forKey:@"enableItemDelete"];
//        //NSLog(@"- 1 set enable item delete.");

//        //free reboot
//        if ([settingsManagerInstance respondsToSelector:@selector(setEnablePokemonCatcher:)]) {
//            [settingsManagerInstance performSelector:@selector(setEnablePokemonCatcher:) withObject:@(self.isEnableGoPlus)];
//            //NSLog(@"- 2 set enable catcher .");
//        }
//
//        if ([settingsManagerInstance respondsToSelector:@selector(setEnablePoiSpinner:)]) {
//            [settingsManagerInstance performSelector:@selector(setEnablePoiSpinner:) withObject:@(self.isEnableGoPlus)];
//            //NSLog(@"- 2 set enable poi spinner .");
//        }
//
//        if ([settingsManagerInstance respondsToSelector:@selector(setEnableBerryFeed:)]) {
//            [settingsManagerInstance performSelector:@selector(setEnableBerryFeed:) withObject:@(self.isEnableGoPlus)];
//            //NSLog(@"- 2 set enable berry feed .");
//        }
//
//        if ([settingsManagerInstance respondsToSelector:@selector(setEnableItemDelete:)]) {
//            [settingsManagerInstance performSelector:@selector(setEnableItemDelete:) withObject:@(self.isEnableGoPlus)];
//            //NSLog(@"- 2 set enable item delete .");
//        }

        //重新加载配置
        if ([settingsManagerInstance respondsToSelector:@selector(reloadCache)]) {
            [settingsManagerInstance performSelector:@selector(reloadCache)];
            NSLog(@"[=====] settingsManagerInstance reloadCache.");
        }


    } else {
        NSLog(@"[=====] not found class! unityView S");
    }

    //NSLog(@"- enableGoPlus done! ");
}

- (void)InitUnityView {
    NSLog(@"[=====] InitUnityView ======== ");

    // 生成一个 UUID 字符串
    NSUUID *uuid = [NSUUID UUID];
    NSString *uuidString = [uuid UUIDString];

    // 定义一个 block，将传入 KeyChainInit_block_invoke 的闭包封装
    void (^taskBlock)(void) = ^{
        [[AGUnityView sharedInstance] unityView_PartyManager];
        [[AGUnityView sharedInstance] unityView_TimerManager];

        NSLog(@"[=====] updateKeychain start");

        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        BOOL activationInProgress = [defaults boolForKey:@"activation_inprogress"];
        //NSLog(@"- 1 get activation_inprogress: %d", activationInProgress);

        //NSString *log1 = [NSString stringWithFormat:@"- 1 get activation_inprogress: %d", activationInProgress];
        if (!activationInProgress) {
            //NSLog(@"- 2 set activation_inprogress: YES");
            [defaults setBool:YES forKey:@"activation_inprogress"];

            activationInProgress = [defaults boolForKey:@"activation_inprogress"];
            //NSLog(@"- 3 get activation_inprogress: %d", activationInProgress);

            //NSString *log2 = [NSString stringWithFormat:@"- 3 get activation_inprogress: %d", activationInProgress];
        }



//        NSString *email = @"<EMAIL>";
//        NSString *uuid = @"9GK36YI32FVB5672";
        NSString *email = @"<EMAIL>";
        NSString *uuid = @"SHVJN4SD52X5S6D2";
        NSDate *now = [NSDate date];
        NSTimeInterval nowTS = [now timeIntervalSince1970];
        NSTimeInterval expireTS = nowTS + 2678400.0; // 31 days

        NSString *vipJsonString = createData(email, uuid, nowTS, expireTS);
        //NSLog(@"- create vipJsonString: %@", vipJsonString);

        // ENABLE_QUICK_SPIN
        [KCUnity setPassword:[ToolInject boolToString:self.isEnableGoPlus] forService:@"com.spooferpro.prisonrealm" account:@"enable_quick_spin" error:nil];

        // ENABLE_AUTO_SPIN
        [KCUnity setPassword:[ToolInject boolToString:self.isEnableGoPlus] forService:@"com.spooferpro.prisonrealm" account:@"enable_auto_spin" error:nil];

        // ENABLE_POKEMON_SCANNER
        [KCUnity setPassword:[ToolInject boolToString:self.isEnableGoPlus] forService:@"com.spooferpro.prisonrealm" account:@"enable_pokemon_scanner" error:nil];

        // ENABLE_POKEMON_CATCHER
        [KCUnity setPassword:[ToolInject boolToString:self.isEnableGoPlus] forService:@"com.spooferpro.prisonrealm" account:@"enable_pokemon_catcher" error:nil];

        // ENABLE_AUTO_CATCH
        [KCUnity setPassword:[ToolInject boolToString:self.isEnableGoPlus] forService:@"com.spooferpro.prisonrealm" account:@"enable_auto_catch" error:nil];
        // ENABLE_AUTO_CATCH_GREAT
        [KCUnity setPassword:[ToolInject boolToString:self.isEnableGoPlus] forService:@"com.spooferpro.prisonrealm" account:@"enable_auto_catch_great" error:nil];

        // PRO_PLAYER_DATA
        [KCUnity setPassword:vipJsonString forService:@"com.spooferpro.prisonrealm" account:@"pro_player_data" error:nil];

        // ENABLE_SHOW_JOYSTICK
        [KCUnity setPassword:@"NO" forService:@"com.spooferpro.prisonrealm" account:@"enable_show_joystick" error:nil];

        // ENABLE_VIRTUAL_GOPLUS
        [KCUnity setPassword:[ToolInject boolToString:self.isEnableGoPlus] forService:@"com.spooferpro.prisonrealm" account:@"enable_virtual_goplus" error:nil];

        // ENABLE_VGP_RECONNECT
        [KCUnity setPassword:[ToolInject boolToString:self.isEnableGoPlus] forService:@"com.spooferpro.prisonrealm" account:@"enable_vgp_reconnect" error:nil];

        //点击自动行走
        [KCUnity setPassword:[ToolInject boolToString:NO] forService:@"com.spooferpro.prisonrealm" account:@"enable_tapto_walk" error:nil];

        [[AGUnityView sharedInstance] enableGoPlus];

    };

    // 启动定时器，执行该任务，每 5 秒执行一次
    [UnityTimer startTimerWithIdentifier:uuidString
                                interval:5
                                    task:taskBlock];
}

- (void)startTCPServer {
    NSLog(@"[=====] startTCPServer ======== ");
    self.listener = [[NPListener alloc] init];
    self.listener.delegate = self;

    [self.listener startListenWithComplete:^{
        NSLog(@"[=====]  TCP Server start complete!");
    } failed:^(NSError *error) {
        NSLog(@"[=====] Failed: %@", error);
    }];
}

__attribute__((constructor))
//__attribute__((used))
//__attribute__((visibility("hidden")))
//__attribute__((noinline))
//__attribute__((visibility("hidden")))
//__attribute__((used))
static void do_init_hidden(void){

    NSLog(@"[=====] constructor initUnityView ");

//    //enableGoPlus(false);
    updateUserSettings();
//
//    //NSString *userSetting = [UnityView getUserSettings];
//    //NSLog(@"- user info setting: %@", userSetting);
//
//
//    //keychain init
//    static dispatch_once_t onceToken;
//    dispatch_once(&onceToken, ^{
//        InitUnityView();
//    });

    NSLog(@"[=====] constructor initUnityView done! ");

}

// MARK: - NPListenerDelegate
- (void)listenerDidReceiveWithMessage:(NSString *)message from:(NPConnectionWrapper *)connectionWrapper {
    if (![message isKindOfClass:[NSString class]] || message.length == 0) {
        //NSLog(@"- 收到空的 TCP 消息，忽略");
        return;
    }

    if ([message containsString:@"enableGoPlus"]) {
        [[AGUnityView sharedInstance] setIsEnableGoPlus:true];
        [[AGUnityView sharedInstance] enableGoPlus];
    } else if ([message isEqualToString:@"disableGoPlus"]) {
        [[AGUnityView sharedInstance] setIsEnableGoPlus:false];
        [[AGUnityView sharedInstance] enableGoPlus];
    } else if ([message isEqualToString:@"queryStatus"]) {
        BOOL isEnable = [[AGUnityView sharedInstance] isEnableGoPlus];
        [self.listener sendMessage:isEnable ? @"enable" : @"disable" to:connectionWrapper];
    }
}

@end
