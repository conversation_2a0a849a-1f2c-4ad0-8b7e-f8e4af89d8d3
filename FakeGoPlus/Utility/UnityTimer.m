//
//  UnityTimer.m
//  UnityPlugin
//
//  Created by lsc on 2025/5/12.
//

#import "UnityTimer.h"
#import <dispatch/dispatch.h>


@implementation UnityTimer

- (instancetype)init {
    self = [super init];
    if (self) {
        _timers = [NSMutableDictionary dictionary];
    }
    return self;
}

+ (instancetype)sharedInstance {
    static UnityTimer *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[UnityTimer alloc] init];
    });
    return instance;
}

+ (void)startTimerWithIdentifier:(NSString *)identifier
                        interval:(NSTimeInterval)interval
                            task:(void (^)(void))task {
    if (identifier == nil || [identifier length] == 0) {
        return;
    }
    [self stopTimerWithIdentifier:identifier];
    
    dispatch_queue_t queue = dispatch_get_main_queue();
    dispatch_source_t timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, queue);
    dispatch_source_set_timer(timer, dispatch_time(DISPATCH_TIME_NOW, 0), interval * NSEC_PER_SEC, 0);
        dispatch_source_set_event_handler(timer, ^{
        if (task) {
            task();
        }
    });
    dispatch_resume(timer);
    [[UnityTimer sharedInstance].timers setObject:timer forKey:identifier];
}

+ (void)stopTimerWithIdentifier:(NSString *)identifier {
    dispatch_source_t timer = [[UnityTimer sharedInstance].timers objectForKey:identifier];
    if (timer) {
        dispatch_source_cancel(timer);
        [[UnityTimer sharedInstance].timers removeObjectForKey:identifier];
    }
}

@end




