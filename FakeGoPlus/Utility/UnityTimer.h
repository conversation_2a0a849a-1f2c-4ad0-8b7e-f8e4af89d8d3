//
//  UnityTimer.h
//  UnityPlugin
//
//  Created by lsc on 2025/5/12.
//

#import <Foundation/Foundation.h>


@interface UnityTimer : NSObject

@property (nonatomic, strong) NSMutableDictionary *timers;

+ (instancetype)sharedInstance;
+ (void)startTimerWithIdentifier:(NSString *)identifier
                        interval:(NSTimeInterval)interval
                            task:(void (^)(void))task;
+ (void)stopTimerWithIdentifier:(NSString *)identifier;


@end

