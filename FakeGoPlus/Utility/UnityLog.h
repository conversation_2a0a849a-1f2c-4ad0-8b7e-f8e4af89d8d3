//
//  UnityLog.h
//  UnityPlugin
//
//  Created by lsc on 2025/5/12.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, LogLevel) {
    LogLevelInfo,
    LogLevelWarning,
    LogLevelError
};

@interface UnityLog : NSObject

@property (nonatomic, strong) NSString *logFilePath;

+ (instancetype)sharedInstance;
- (void)logMessage:(NSString *)message level:(LogLevel)level;
- (void)clearLogFile;

@end

NS_ASSUME_NONNULL_END
