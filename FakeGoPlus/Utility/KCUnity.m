
//  Copyright (c) 2010-2014 <PERSON>. All rights reserved.
//

#import "KCUnity.h"
#import "KCUnityQuery.h"

NSString * const kKCUnityErrorDomain = @"com.flipboard.flex";
NSString * const kKCUnityAccountKey = @"acct";
NSString * const kKCUnityCreatedAtKey = @"cdat";
NSString * const kKCUnityClassKey = @"labl";
NSString * const kKCUnityDescriptionKey = @"desc";
NSString * const kKCUnityGroupKey = @"agrp";
NSString * const kKCUnityLabelKey = @"labl";
NSString * const kKCUnityLastModifiedKey = @"mdat";
NSString * const kKCUnityWhereKey = @"svce";

#if __IPHONE_4_0 && TARGET_OS_IPHONE
static CFTypeRef KCUnityAccessibilityType = NULL;
#endif


@implementation KCUnity

+ (NSString *)passwordForService:(NSString *)serviceName account:(NSString *)account {
    return [self passwordForService:serviceName account:account error:nil];
}

+ (NSString *)passwordForService:(NSString *)serviceName account:(NSString *)account error:(NSError *__autoreleasing *)error {
    KCUnityQuery *query = [KCUnityQuery new];
    query.service = serviceName;
    query.account = account;
    [query fetch:error];
    return query.password;
}

+ (NSData *)passwordDataForService:(NSString *)serviceName account:(NSString *)account {
    return [self passwordDataForService:serviceName account:account error:nil];
}

+ (NSData *)passwordDataForService:(NSString *)serviceName account:(NSString *)account error:(NSError **)error {
    KCUnityQuery *query = [KCUnityQuery new];
    query.service = serviceName;
    query.account = account;
    [query fetch:error];
    
    return query.passwordData;
}

+ (BOOL)deletePasswordForService:(NSString *)serviceName account:(NSString *)account {
    return [self deletePasswordForService:serviceName account:account error:nil];
}

+ (BOOL)deletePasswordForService:(NSString *)serviceName account:(NSString *)account error:(NSError *__autoreleasing *)error {
    KCUnityQuery *query = [KCUnityQuery new];
    query.service = serviceName;
    query.account = account;
    return [query deleteItem:error];
}

+ (BOOL)setPassword:(NSString *)password forService:(NSString *)serviceName account:(NSString *)account {
    return [self setPassword:password forService:serviceName account:account error:nil];
}

+ (BOOL)setPassword:(NSString *)password forService:(NSString *)serviceName account:(NSString *)account error:(NSError *__autoreleasing *)error {
    KCUnityQuery *query = [KCUnityQuery new];
    query.service = serviceName;
    query.account = account;
    query.password = password;
    return [query save:error];
}

+ (BOOL)setPasswordData:(NSData *)password forService:(NSString *)serviceName account:(NSString *)account {
    return [self setPasswordData:password forService:serviceName account:account error:nil];
}

+ (BOOL)setPasswordData:(NSData *)password forService:(NSString *)serviceName account:(NSString *)account error:(NSError **)error {
    KCUnityQuery *query = [KCUnityQuery new];
    query.service = serviceName;
    query.account = account;
    query.passwordData = password;
    return [query save:error];
}

+ (NSArray *)allAccounts {
    return [self allAccounts:nil] ?: @[];
}

+ (NSArray *)allAccounts:(NSError *__autoreleasing *)error {
    return [self accountsForService:nil error:error];
}

+ (NSArray *)accountsForService:(NSString *)serviceName {
    return [self accountsForService:serviceName error:nil];
}

+ (NSArray *)accountsForService:(NSString *)serviceName error:(NSError *__autoreleasing *)error {
    KCUnityQuery *query = [KCUnityQuery new];
    query.service = serviceName;
    return [query fetchAll:error];
}

#if __IPHONE_4_0 && TARGET_OS_IPHONE
+ (CFTypeRef)accessibilityType {
    return KCUnityAccessibilityType;
}

+ (void)setAccessibilityType:(CFTypeRef)accessibilityType {
    CFRetain(accessibilityType);
    if (KCUnityAccessibilityType) {
        CFRelease(KCUnityAccessibilityType);
    }
    KCUnityAccessibilityType = accessibilityType;
}
#endif

@end
