//
//  ToolInject.m
//  UnityPlugin
//
//  Created by lsc on 2025/5/12.
//

#import <Foundation/Foundation.h>
#import "objc/runtime.h"
#import "UnityLog.h"


NSString *createData(NSString *email, NSString *licenceKey, NSTimeInterval validFrom, NSTimeInterval validTill) {
    NSMutableDictionary *licenceInfo = [NSMutableDictionary dictionary];
    licenceInfo[@"licence_id"] = @"28156";
    licenceInfo[@"licence_type"] = @"3";
    licenceInfo[@"license_email"] = email ?: @"";
    licenceInfo[@"discord_name"] = @"NA";
    licenceInfo[@"licence_key"] = licenceKey ?: @"";
    licenceInfo[@"valid_from"] = [@(validFrom) stringValue];
    licenceInfo[@"valid_till"] = [@(validTill) stringValue];
    licenceInfo[@"licence_status"] = @"1";

    NSDictionary *responseDict = @{
        @"request_status": @"success",
        @"request_message": @"licence info",
        @"request_output": licenceInfo
    };

    NSError *error = nil;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:responseDict options:NSJSONWritingPrettyPrinted error:&error];
    if (error) {
        NSLog(@"Feature Error creating JSON: %@", error.localizedDescription);
        return nil;
    }

    return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
}



void HookMethod(Class originalCls,Class swizzledCls,SEL originalSelector, SEL swizzledSelector) {
    if (!originalCls || !swizzledCls) {
        NSLog(@"- class[1/2] can't be null!");
        [[UnityLog sharedInstance] logMessage:@"- class[1/2] can't be null!" level:LogLevelError];
        return;
    }
    if (!originalSelector || !swizzledSelector) {
        NSLog(@"- method[1/2] can't be null!");
        [[UnityLog sharedInstance] logMessage:@"- method[1/2] can't be null!" level:LogLevelError];
        return;
    }
    Method originalMethod = class_getInstanceMethod(originalCls, originalSelector);
    Method swizzledMethod = class_getInstanceMethod(swizzledCls, swizzledSelector);
    BOOL didAddMethod = class_addMethod(originalCls,
                                        swizzledSelector,
                                        method_getImplementation(swizzledMethod),
                                        method_getTypeEncoding(swizzledMethod));
    if (didAddMethod) {
        NSLog(@"- hook method success: %@.%@ to %@.%@", NSStringFromClass(originalCls), NSStringFromSelector(originalSelector), NSStringFromClass(swizzledCls), NSStringFromSelector(swizzledSelector));
        NSString *logMessage = [NSString stringWithFormat:@"- hook method success: %@.%@ to %@.%@", NSStringFromClass(originalCls), NSStringFromSelector(originalSelector), NSStringFromClass(swizzledCls), NSStringFromSelector(swizzledSelector)];
        [[UnityLog sharedInstance] logMessage:logMessage level:LogLevelError];
        method_exchangeImplementations(originalMethod, class_getInstanceMethod(originalCls, swizzledSelector));
    } else {
        NSLog(@"- hook method failed.");
        [[UnityLog sharedInstance] logMessage:@"- hook method failed." level:LogLevelError];
    }
}





@implementation ToolInject: NSObject

+ (id)callClassMethod:(SEL)selector onClass:(Class)cls {
    return [self callInstanceMethod:selector onObject:cls withArguments:nil];
}

+ (id)callInstanceMethod:(SEL)selector onObject:(id)object {
    return [self callInstanceMethod:selector onObject:object withArguments:nil];
}

+ (id)callInstanceMethod:(SEL)selector onObject:(id)object withArguments:(NSArray *)arguments {
    NSMethodSignature *signature = [object methodSignatureForSelector:selector];
    if (!signature) {
        NSLog(@"- not found selector: %@", NSStringFromSelector(selector));
        return nil;
    }
    NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:signature];
    [invocation setSelector:selector];
    [invocation setTarget:object];
    if (arguments) {
        for (NSUInteger i = 0; i < arguments.count; i++) {
            id argument = arguments[i];
            [invocation setArgument:&argument atIndex:i + 2];
        }
    }
    [invocation invoke];
    id returnValue = nil;
    if (signature.methodReturnLength) {
        [invocation getReturnValue:&returnValue];
    }
    return returnValue;
}

+ (id)createInstanceWithClass:(Class)cls selector:(SEL)selector withArguments:(NSArray *)arguments {
    id instance = nil;
    if (selector) {
        instance = [[cls alloc] init];
        if (instance) {
            [self callInstanceMethod:selector onObject:instance withArguments:arguments];
        }
    }
    return instance;
}


+ (NSString *) boolToString:(BOOL) value {
    return value ? @"YES" : @"NO";
}

@end




