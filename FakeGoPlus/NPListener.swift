//
//  NPListener.swift
//  TCPServer
//
//  Created by lsc on 2025/5/15.
//  Enhanced for background support

import Foundation
import Network
import UIKit

// MARK: - Connection Wrapper for Objective-C compatibility
@objc public class NPConnectionWrapper: NSObject {
    internal let connection: NWConnection

    internal init(connection: NWConnection) {
        self.connection = connection
        super.init()
    }

    @objc public var isReady: Bool {
        return connection.state == .ready
    }

    @objc public var endpoint: String {
        return connection.endpoint.debugDescription
    }
}

// MARK: - Objective-C Compatible Protocol
@objc public protocol NPListenerDelegate {
    func listenerDidReceive(message: String, from connectionWrapper: NPConnectionWrapper)
    @objc optional func listenerWillEnterBackground()
    @objc optional func listenerDidEnterForeground()
}

// MARK: - Main Listener Class with Background Support
@objc public class NPListener: NSObject {
    @objc dynamic public weak var delegate: NPListenerDelegate?

    private var listener: NWListener?
    private var connectedClients: [NWConnection] = []
    private var connectionWrappers = NSMapTable<NWConnection, NPConnectionWrapper>.strongToStrongObjects()
    private let queue = DispatchQueue(label: "com.itoolab.pogoplus")

    // 后台任务相关
    private var backgroundTask: UIBackgroundTaskIdentifier = .invalid
    private var backgroundTimer: Timer?
    private var isInBackground = false

    // 重连机制
    private var shouldAutoReconnect = true
    private var reconnectTimer: Timer?
    private var maxReconnectAttempts = 10 // 增加重连次数
    private var currentReconnectAttempts = 0

    // 心跳机制
    private var heartbeatTimer: Timer?
    private let heartbeatInterval: TimeInterval = 30.0

    @objc public static let shared = NPListener()

    @objc public override init() {
        super.init()
        setupNotifications()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
        stopBackgroundTask()
        stopHeartbeat()
        stopReconnectTimer()
    }

    // MARK: - Notification Setup
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterBackground),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterForeground),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillTerminate),
            name: UIApplication.willTerminateNotification,
            object: nil
        )
    }

    // MARK: - Background/Foreground Handling
    @objc private func appWillEnterBackground() {
        debugPrint("[=====] App entering background")
        isInBackground = true
        delegate?.listenerWillEnterBackground?()

        // 开始后台任务
        startBackgroundTask()

        // 开始心跳检测
        startHeartbeat()

        // 设置后台定时器，尝试保持连接活跃
        startBackgroundTimer()
    }

    @objc private func appDidEnterForeground() {
        debugPrint("[=====] App entering foreground")
        isInBackground = false
        delegate?.listenerDidEnterForeground?()

        // 停止后台相关定时器
        stopBackgroundTask()
        stopBackgroundTimer()
        stopHeartbeat()

        // 检查连接状态并重连
        checkAndReconnectIfNeeded()
    }

    @objc private func appWillTerminate() {
        stop()
    }

    // MARK: - Background Task Management
    private func startBackgroundTask() {
        guard backgroundTask == .invalid else { return }

        backgroundTask = UIApplication.shared.beginBackgroundTask { [weak self] in
            self?.stopBackgroundTask()
        }
    }

    private func stopBackgroundTask() {
        guard backgroundTask != .invalid else { return }

        UIApplication.shared.endBackgroundTask(backgroundTask)
        backgroundTask = .invalid
    }

    // MARK: - Background Timer
    private func startBackgroundTimer() {
        stopBackgroundTimer()

        // 调整为与心跳间隔一致，避免冲突
        backgroundTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            self?.handleBackgroundKeepAlive()
        }
    }

    private func stopBackgroundTimer() {
        backgroundTimer?.invalidate()
        backgroundTimer = nil
    }

    private func handleBackgroundKeepAlive() {
        // 检查连接状态并清理断开的连接
        var disconnectedConnections: [NWConnection] = []

        for connection in connectedClients {
            if connection.state != .ready {
                disconnectedConnections.append(connection)
            }
        }

        // 移除断开的连接
        for connection in disconnectedConnections {
            removeConnection(connection)
        }

        // 发送心跳包给所有活跃连接的客户端
        if !connectedClients.isEmpty {
            broadcastMessage("HEARTBEAT")
        }

        // 重新申请后台执行时间
        if backgroundTask != .invalid {
            stopBackgroundTask()
            startBackgroundTask()
        }

        debugPrint("[=====] Background keep-alive executed, active connections: \(connectedClients.count)")
    }

    // MARK: - Heartbeat Mechanism
    private func startHeartbeat() {
        stopHeartbeat()

        heartbeatTimer = Timer.scheduledTimer(withTimeInterval: heartbeatInterval, repeats: true) { [weak self] _ in
            self?.sendHeartbeat()
        }
    }

    private func stopHeartbeat() {
        heartbeatTimer?.invalidate()
        heartbeatTimer = nil
    }

    private func sendHeartbeat() {
        // 检查所有连接的状态
        var disconnectedConnections: [NWConnection] = []

        for connection in connectedClients {
            if connection.state == .ready {
                // 发送心跳包
                sendMessage("PING", on: connection)
            } else {
                disconnectedConnections.append(connection)
            }
        }

        // 移除断开的连接
        for connection in disconnectedConnections {
            removeConnection(connection)
        }

        debugPrint("[=====] Heartbeat sent to \(connectedClients.count) connections")
    }

    // MARK: - Reconnect Mechanism
    private func startReconnectTimer() {
        stopReconnectTimer()

        reconnectTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: false) { [weak self] _ in
            self?.attemptReconnect()
        }
    }

    private func stopReconnectTimer() {
        reconnectTimer?.invalidate()
        reconnectTimer = nil
    }

    private func attemptReconnect() {
        guard shouldAutoReconnect && currentReconnectAttempts < maxReconnectAttempts else {
            debugPrint("[=====] Max reconnect attempts reached")
            return
        }

        currentReconnectAttempts += 1
        debugPrint("[=====] Attempting reconnect \(currentReconnectAttempts)/\(maxReconnectAttempts)")

        // 重启监听器
        startListen(
            complete: {
                debugPrint("[=====] Reconnect successful")
                self.currentReconnectAttempts = 0
            },
            failed: { error in
                debugPrint("[=====] Reconnect failed: \(error)")
                self.startReconnectTimer()
            }
        )
    }

    private func checkAndReconnectIfNeeded() {
        if listener?.state != .ready && shouldAutoReconnect {
            currentReconnectAttempts = 0
            attemptReconnect()
        }
    }

    // MARK: - Original Methods (Enhanced)
    @objc public func startListen(
        complete: @escaping () -> Void,
        failed: @escaping (NSError) -> Void
    ) {
        do {
            let params = NWParameters.tcp

            // 启用后台网络访问
            params.allowLocalEndpointReuse = true

            listener = try NWListener(using: params, on: 8897)

            listener?.stateUpdateHandler = { [weak self] state in
                guard let self = self else { return }
                switch state {
                case .ready:
                    debugPrint("[=====] Server start listening")
                    self.currentReconnectAttempts = 0
                    complete()
                case .failed(let error):
                    debugPrint("[=====] Server start failed with error: \(error)")
                    failed(error as NSError)

                    // 如果在后台，尝试重连
                    if self.isInBackground && self.shouldAutoReconnect {
                        self.startReconnectTimer()
                    }
                case .cancelled:
                    debugPrint("[=====] Server cancelled")
                default:
                    break
                }
            }

            listener?.newConnectionHandler = { [weak self] connection in
                guard let self = self else { return }
                debugPrint("[=====] Get new connection")
                self.handleConnection(connection)
            }

            listener?.start(queue: queue)
        } catch {
            debugPrint("[=====] Get error: \(error)")
            failed(error as NSError)
        }
    }

    @objc public func stop() {
        shouldAutoReconnect = false
        stopReconnectTimer()
        stopHeartbeat()
        stopBackgroundTask()
        stopBackgroundTimer()

        for client in connectedClients {
            client.cancel()
        }
        connectedClients.removeAll()
        connectionWrappers.removeAllObjects()

        listener?.cancel()
        listener = nil
        debugPrint("[=====] Server stopped")
    }

    // MARK: - Enhanced Connection Handling
    private func handleConnection(_ connection: NWConnection) {
        debugPrint("[=====] New connection from: ")
        connectedClients.append(connection)

        let wrapper = NPConnectionWrapper(connection: connection)
        connectionWrappers.setObject(wrapper, forKey: connection)

        connection.stateUpdateHandler = { [weak self, weak connection] state in
            guard let self = self, let connection = connection else { return }
            switch state {
            case .ready:
                print("[=====] 客户端已连接")
                self.receiveData(from: connection)
            case .failed(let error):
                print("[=====] 连接失败: \(error.localizedDescription)")
                self.removeConnection(connection)
            case .cancelled:
                print("[=====] 连接已取消")
                self.removeConnection(connection)
            default:
                break
            }
        }

        connection.start(queue: queue)
    }

    private func removeConnection(_ connection: NWConnection) {
        connection.cancel()
        if let index = connectedClients.firstIndex(where: { $0 === connection }) {
            connectedClients.remove(at: index)
            connectionWrappers.removeObject(forKey: connection)
            print("[=====] Server: 客户端已断开连接，当前连接数: \(connectedClients.count)")
        }
    }

    private func receiveData(from connection: NWConnection) {
        connection.receive(minimumIncompleteLength: 1, maximumLength: 65536) { [weak self, weak connection] data, _, isComplete, error in
            guard let self = self, let connection = connection else { return }

            if let data = data, !data.isEmpty {
                if let message = String(data: data, encoding: .utf8) {
                    // 过滤心跳消息，避免日志污染
                    if message != "PING" && message != "PONG" && message != "HEARTBEAT" {
                        debugPrint("[=====] Server received message: \(message)")

                        DispatchQueue.main.async {
                            if let wrapper = self.connectionWrappers.object(forKey: connection) {
                                self.delegate?.listenerDidReceive(message: message, from: wrapper)
                            }
                        }
                    } else if message == "PING" {
                        // 响应心跳
                        self.sendMessage("PONG", on: connection)
                    } else if message == "PONG" {
                        // 客户端响应了心跳，更新连接状态
                        debugPrint("[=====] Server received PONG from client")
                    }
                }

                self.receiveData(from: connection)
            } else if isComplete {
                print("[=====] Server: 连接已关闭")
                self.removeConnection(connection)
            } else if let error = error {
                print("[=====] Server: 接收数据错误: \(error.localizedDescription)")
                self.removeConnection(connection)
            }
        }
    }

    // MARK: - Public Methods (Enhanced)
    @objc public func sendMessage(_ message: String, to connectionWrapper: NPConnectionWrapper) {
        sendMessage(message, on: connectionWrapper.connection)
    }

    public func sendMessage(_ message: String, on connection: NWConnection) {
        let data = message.data(using: .utf8)
        connection.send(content: data, completion: .contentProcessed { error in
            if let error = error {
                NSLog("[=====] [NPListener] Error sending message: \(error)")
            }
        })
    }

    @objc public func sendData(_ data: Data, to connectionWrapper: NPConnectionWrapper) {
        send(data: data, to: connectionWrapper.connection)
    }

    func send(data: Data, to connection: NWConnection) {
        connection.send(content: data, completion: .contentProcessed { error in
            if let error = error {
                print("[=====] 发送数据错误: \(error.localizedDescription)")
            }
        })
    }

    @objc public func broadcast(data: Data) {
        for client in connectedClients {
            send(data: data, to: client)
        }
    }

    @objc public func broadcastMessage(_ message: String) {
        guard let data = message.data(using: .utf8) else { return }
        broadcast(data: data)
    }

    @objc public func getAllConnections() -> [NPConnectionWrapper] {
        var wrappers: [NPConnectionWrapper] = []
        for connection in connectedClients {
            if let wrapper = connectionWrappers.object(forKey: connection) {
                wrappers.append(wrapper)
            }
        }
        return wrappers
    }

    @objc public var connectionCount: Int {
        return connectedClients.count
    }

    // MARK: - Configuration Methods
    @objc public func setAutoReconnect(_ enabled: Bool) {
        shouldAutoReconnect = enabled
    }

    @objc public func setMaxReconnectAttempts(_ attempts: Int) {
        maxReconnectAttempts = attempts
    }
}
