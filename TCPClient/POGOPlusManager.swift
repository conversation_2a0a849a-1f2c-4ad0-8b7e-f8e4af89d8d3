//
//  POGOPlusManager.swift
//  AnyGoiOS
//
//  Created by Subo on 21/05/2025.
//  Enhanced for background support

import Foundation
import UIKit

@objc(AGPOGOPlusManager) public class POGOPlusManager: NSObject {
    private lazy var client = TCPClient(host: "localhost", port: 8897)

    @objc public static let shared = POGOPlusManager()

    @objc public var isConnected = false
    @objc public var isConnecting = false
    @objc public var didGetStatus = false
    @objc public var isPOGOPlusEnable = false

    // 连接状态回调
    private var connectionStateHandlers: [(Bool) -> Void] = []

    public override init() {
        super.init()
        setupNotifications()
        setupClient()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - Notification Setup
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterBackground),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterForeground),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
    }

    // MARK: - Background/Foreground Handling
    @objc private func appWillEnterBackground() {
        print("[=====] POGOPlusManager: App entering background")
        // 可以在这里做一些状态保存
    }

    @objc private func appDidEnterForeground() {
        print("[=====] POGOPlusManager: App entering foreground")

        // 回到前台时检查连接状态并查询状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            if self.isConnected {
                self.queryStatus()
            }
        }
    }

    // MARK: - Client Setup
    private func setupClient() {
        // 启用自动重连
        client.setAutoReconnect(true)
        client.setMaxReconnectAttempts(100000)

        client.onMessage { [weak self] message in
            FMLogger.logInfo("Client recevied message: \(message)")
            guard let self else { return }
            DispatchQueue.main.async {
                self.process(message: message)
            }
        }
    }

    @objc public func startClient(_ completion: @escaping (Bool) -> Void) {
        guard !isConnecting else {
            // 如果正在连接，将回调添加到队列中
            connectionStateHandlers.append(completion)
            return
        }

        isConnecting = true
        connectionStateHandlers.append(completion)

        FMLogger.logInfo("Start client...")

        // 使用重试机制：最多10次尝试，每次间隔2秒，每次连接超时20秒
        client.connectWithRetry(maxRetries: 100000, retryInterval: 2.0, timeout: 20) { [weak self] success in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.isConnected = success
                self.isConnecting = false

                if success {
                    FMLogger.logInfo("Client connect server successs.")
                    // 查询状态
                    self.queryStatus()
                } else {
                    FMLogger.logInfo("Client connect server failed")
                }

                // 调用所有等待的回调
                for handler in self.connectionStateHandlers {
                    handler(success)
                }
                self.connectionStateHandlers.removeAll()
            }
        }
    }

    @objc public func closeClient() {
        client.disconnect()
        isConnected = false
        isConnecting = false
        didGetStatus = false

        // 发送连接状态改变通知
        NotificationCenter.default.post(
            name: NSNotification.Name(rawValue: "AGPOGOPlusConnectionChanged"),
            object: nil,
            userInfo: ["connected": false]
        )
    }

    @objc public func sendMessage(_ message: String) {
        guard client.isConnected else {
            FMLogger.logInfo("Client unConnect，Cann't send message: \(message)")
            return
        }

        client.send(message: message) { success in
            if success {
                print("Success to send message: \(message)")
            } else {
                print("Failed to send message: \(message)")
            }
        }
    }

    @objc public func enableGoPlus() {
        sendMessage("enableGoPlus")

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.queryStatus()
        }
    }

    @objc public func disableGoPlus() {
        sendMessage("disableGoPlus")

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.queryStatus()
        }
    }

    @objc public func queryStatus() {
        sendMessage("queryStatus")
    }

    // MARK: - Enhanced Message Processing
    private func process(message: String) {
        // 过滤心跳消息，不需要处理
        if message == "PING" || message == "PONG" || message == "HEARTBEAT" {
            return
        }

        if message == "enable" {
            updatePOGOPlusEnable(true)
        } else if message == "disable" {
            updatePOGOPlusEnable(false)
        } else {
            // 处理其他可能的消息
            print("Client recevied unknown: \(message)")
        }
    }

    private func updatePOGOPlusEnable(_ enable: Bool) {
        didGetStatus = true
        isPOGOPlusEnable = enable

        NotificationCenter.default.post(
            name: NSNotification.Name(rawValue: "AGPOGOPlusStatusChanged"),
            object: nil,
            userInfo: [
                "enabled": enable,
                "timestamp": Date()
            ]
        )

        print("Current POGOPlus status: \(enable ? "enable" : "disable")")
    }

    // MARK: - Public Status Methods
    @objc public var connectionStatus: String {
        if isConnecting {
            return "connecting"
        } else if isConnected {
            return "connected"
        } else {
            return "disconnected"
        }
    }

    @objc public func forceReconnect() {
        if isConnected {
            closeClient()
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.startClient { success in
                print("Client force connect: \(success)")
            }
        }
    }

    // MARK: - Connection State Monitoring
    @objc public func addConnectionStateHandler(_ handler: @escaping (Bool) -> Void) {
        if isConnecting {
            connectionStateHandlers.append(handler)
        } else {
            handler(isConnected)
        }
    }
}
