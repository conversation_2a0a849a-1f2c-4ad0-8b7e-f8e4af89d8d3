//
//  POGOPlusManager.swift
//  AnyGoiOS
//
//  Created by Subo on 21/05/2025.
//  Enhanced for background support

import Foundation
import UIKit

@objc(AGPOGOPlusManager) public class POGOPlusManager: NSObject {
    private lazy var client = TCPClient(host: "localhost", port: 8897)

    @objc public static let shared = POGOPlusManager()

    @objc public var isConnected = false
    @objc public var isConnecting = false
    @objc public var didGetStatus = false
    @objc public var isPOGOPlusEnable = false

    // 连接状态回调
    private var connectionStateHandlers: [(Bool) -> Void] = []

    // 连接监控定时器
    private var connectionMonitorTimer: Timer?

    public override init() {
        super.init()
        setupNotifications()
        setupClient()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
        stopConnectionMonitor()
        // 确保客户端被正确关闭
        if isConnected {
            closeClient()
        }
        FMLogger.logInfo("POGOPlusManager deinit")
    }

    // MARK: - Notification Setup
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterBackground),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterForeground),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
    }

    // MARK: - Background/Foreground Handling
    @objc private func appWillEnterBackground() {
        print("[=====] POGOPlusManager: App entering background")
        // 可以在这里做一些状态保存
    }

    @objc private func appDidEnterForeground() {
        print("[=====] POGOPlusManager: App entering foreground")

        // 回到前台时检查连接状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            if self.isConnected && self.client.isConnected {
                // 连接正常，查询状态
                self.queryStatus()
            } else if !self.isConnecting {
                // 连接断开且没有在连接中，尝试重连
                FMLogger.logInfo("Connection lost, attempting to reconnect...")
                self.startClient { success in
                    if success {
                        FMLogger.logInfo("Foreground reconnection successful")
                    } else {
                        FMLogger.logError("Foreground reconnection failed")
                    }
                }
            }
        }
    }

    // MARK: - Client Setup
    private func setupClient() {
        // 启用自动重连，设置合理的重连次数
        client.setAutoReconnect(true)
        client.setMaxReconnectAttempts(10) // 修改为合理的重连次数

        client.onMessage { [weak self] message in
            FMLogger.logInfo("Client received message: \(message)")
            guard let self else { return }
            DispatchQueue.main.async {
                self.process(message: message)
            }
        }
    }

    @objc public func startClient(_ completion: @escaping (Bool) -> Void) {
        guard !isConnecting else {
            // 如果正在连接，将回调添加到队列中
            connectionStateHandlers.append(completion)
            return
        }

        isConnecting = true
        connectionStateHandlers.append(completion)

        FMLogger.logInfo("Start client...")

        // 使用重试机制：最多100次尝试，每次间隔2秒，每次连接超时20秒
        client.connectWithRetry(maxRetries: 100, retryInterval: 2.0, timeout: 20) { [weak self] success in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.isConnected = success
                self.isConnecting = false

                if success {
                    FMLogger.logInfo("Client connect server success.")
                    // 启动连接监控
                    self.startConnectionMonitor()
                    // 查询状态
                    self.queryStatus()
                } else {
                    FMLogger.logError("Client connect server failed")
                }

                // 调用所有等待的回调
                for handler in self.connectionStateHandlers {
                    handler(success)
                }
                self.connectionStateHandlers.removeAll()
            }
        }
    }

    @objc public func closeClient() {
        FMLogger.logInfo("Closing client connection...")

        stopConnectionMonitor()
        client.disconnect()
        isConnected = false
        isConnecting = false
        didGetStatus = false

        // 清理等待的回调
        for handler in connectionStateHandlers {
            handler(false)
        }
        connectionStateHandlers.removeAll()

        // 发送连接状态改变通知
        NotificationCenter.default.post(
            name: NSNotification.Name(rawValue: "AGPOGOPlusConnectionChanged"),
            object: nil,
            userInfo: ["connected": false]
        )

        FMLogger.logInfo("Client connection closed")
    }

    @objc public func sendMessage(_ message: String) {
        guard client.isConnected else {
            FMLogger.logError("Client not connected, can't send message: \(message)")
            return
        }

        client.send(message: message) { success in
            if success {
                FMLogger.logInfo("Success to send message: \(message)")
            } else {
                FMLogger.logError("Failed to send message: \(message)")
            }
        }
    }

    @objc public func enableGoPlus() {
        sendMessage("enableGoPlus")

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.queryStatus()
        }
    }

    @objc public func disableGoPlus() {
        sendMessage("disableGoPlus")

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.queryStatus()
        }
    }

    @objc public func queryStatus() {
        sendMessage("queryStatus")
    }

    // MARK: - Enhanced Message Processing
    private func process(message: String) {
        // 过滤心跳消息，不需要处理
        if message == "PING" || message == "PONG" || message == "HEARTBEAT" {
            return
        }

        FMLogger.logInfo("Processing message: \(message)")

        if message == "enable" {
            updatePOGOPlusEnable(true)
        } else if message == "disable" {
            updatePOGOPlusEnable(false)
        } else {
            // 处理其他可能的消息
            FMLogger.logInfo("Client received unknown message: \(message)")
        }
    }

    private func updatePOGOPlusEnable(_ enable: Bool) {
        didGetStatus = true
        isPOGOPlusEnable = enable

        NotificationCenter.default.post(
            name: NSNotification.Name(rawValue: "AGPOGOPlusStatusChanged"),
            object: nil,
            userInfo: [
                "enabled": enable,
                "timestamp": Date()
            ]
        )

        FMLogger.logInfo("POGOPlus status updated: \(enable ? "enabled" : "disabled")")
    }

    // MARK: - Public Status Methods
    @objc public var connectionStatus: String {
        if isConnecting {
            return "connecting"
        } else if isConnected {
            return "connected"
        } else {
            return "disconnected"
        }
    }

    @objc public func forceReconnect() {
        if isConnected {
            closeClient()
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.startClient { success in
                print("Client force connect: \(success)")
            }
        }
    }

    // MARK: - Connection Monitoring
    private func startConnectionMonitor() {
        stopConnectionMonitor()

        connectionMonitorTimer = Timer.scheduledTimer(withTimeInterval: 15.0, repeats: true) { [weak self] _ in
            self?.checkConnectionStatus()
        }

        FMLogger.logInfo("Connection monitor started")
    }

    private func stopConnectionMonitor() {
        connectionMonitorTimer?.invalidate()
        connectionMonitorTimer = nil
    }

    private func checkConnectionStatus() {
        // 检查连接状态是否一致
        let actuallyConnected = client.isConnected

        if isConnected != actuallyConnected {
            FMLogger.logInfo("Connection status mismatch detected: isConnected=\(isConnected), actual=\(actuallyConnected)")

            if !actuallyConnected && !isConnecting {
                // 连接实际断开了，但状态还是连接中，尝试重连
                FMLogger.logInfo("Connection lost, attempting automatic reconnection...")
                isConnected = false

                startClient { [weak self] success in
                    if success {
                        FMLogger.logInfo("Automatic reconnection successful")
                    } else {
                        FMLogger.logError("Automatic reconnection failed")
                    }
                }
            }
        }
    }

    // MARK: - Connection State Monitoring
    @objc public func addConnectionStateHandler(_ handler: @escaping (Bool) -> Void) {
        if isConnecting {
            connectionStateHandlers.append(handler)
        } else {
            handler(isConnected)
        }
    }
}
