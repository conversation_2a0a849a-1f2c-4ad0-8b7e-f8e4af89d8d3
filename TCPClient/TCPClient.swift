//
//  TCPClient.swift
//  AnyGoiOS
//
//  Created by <PERSON><PERSON> on 21/05/2025.
//  Enhanced for background support and heartbeat

import Foundation
import Network
import UIKit

class TCPClient {
    private var connection: NWConnection?
    private let host: String
    private let port: UInt16
    private let queue = DispatchQueue(label: "com.itoolab.TCPClientQueue")
    private var messageHandler: ((String) -> Void)?

    // 后台和心跳支持
    private var backgroundTask: UIBackgroundTaskIdentifier = .invalid
    private var isInBackground = false
    private var shouldAutoReconnect = true
    private var reconnectTimer: Timer?
    private var maxReconnectAttempts = 5
    private var currentReconnectAttempts = 0

    // 心跳机制
    private var lastHeartbeatTime = Date()
    private var heartbeatCheckTimer: Timer?
    private let heartbeatTimeout: TimeInterval = 60.0 // 心跳超时时间

    init(host: String, port: UInt16) {
        self.host = host
        self.port = port
        setupNotifications()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
        stopBackgroundTask()
        stopReconnectTimer()
        stopHeartbeatCheck()
    }

    // MARK: - Notification Setup
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterBackground),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterForeground),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
    }

    // MARK: - Background/Foreground Handling
    @objc private func appWillEnterBackground() {
        print("[=====] Client: App entering background")
        isInBackground = true
        startBackgroundTask()
        startHeartbeatCheck()
    }

    @objc private func appDidEnterForeground() {
        print("[=====] Client: App entering foreground")
        isInBackground = false
        stopBackgroundTask()
        stopHeartbeatCheck()

        // 检查连接状态
        checkConnectionAndReconnect()
    }

    // MARK: - Background Task Management
    private func startBackgroundTask() {
        guard backgroundTask == .invalid else { return }

        backgroundTask = UIApplication.shared.beginBackgroundTask { [weak self] in
            self?.stopBackgroundTask()
        }
    }

    private func stopBackgroundTask() {
        guard backgroundTask != .invalid else { return }

        UIApplication.shared.endBackgroundTask(backgroundTask)
        backgroundTask = .invalid
    }

    // MARK: - Heartbeat Check
    private func startHeartbeatCheck() {
        stopHeartbeatCheck()

        heartbeatCheckTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            self?.checkHeartbeat()
        }
    }

    private func stopHeartbeatCheck() {
        heartbeatCheckTimer?.invalidate()
        heartbeatCheckTimer = nil
    }

    private func checkHeartbeat() {
        let timeSinceLastHeartbeat = Date().timeIntervalSince(lastHeartbeatTime)

        if timeSinceLastHeartbeat > heartbeatTimeout {
            print("[=====] Client: Heartbeat timeout, attempting reconnect")
            attemptReconnect()
        } else {
            // 重新申请后台时间
            if backgroundTask != .invalid {
                stopBackgroundTask()
                startBackgroundTask()
            }
        }
    }

    // MARK: - Reconnect Mechanism
    private func startReconnectTimer() {
        stopReconnectTimer()

        reconnectTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: false) { [weak self] _ in
            self?.attemptReconnect()
        }
    }

    private func stopReconnectTimer() {
        reconnectTimer?.invalidate()
        reconnectTimer = nil
    }

    private func attemptReconnect() {
        guard shouldAutoReconnect && currentReconnectAttempts < maxReconnectAttempts else {
            print("[=====] Client: Max reconnect attempts reached")
            return
        }

        currentReconnectAttempts += 1
        print("[=====] Client: Attempting reconnect \(currentReconnectAttempts)/\(maxReconnectAttempts)")

        disconnect()

        connect { [weak self] success in
            if success {
                print("[=====] Client: Reconnect successful")
                self?.currentReconnectAttempts = 0
            } else {
                print("[=====] Client: Reconnect failed")
                self?.startReconnectTimer()
            }
        }
    }

    private func checkConnectionAndReconnect() {
        if connection?.state != .ready && shouldAutoReconnect {
            currentReconnectAttempts = 0
            attemptReconnect()
        }
    }

    // MARK: - Enhanced Connection Methods
    func connect(timeout: TimeInterval = 30.0, completion: @escaping (Bool) -> Void) {
        // 创建NWEndpoint
        let hostEndpoint = NWEndpoint.Host(host)
        let portEndpoint = NWEndpoint.Port(rawValue: port)!

        // 创建TCP连接，启用后台网络访问
        let parameters = NWParameters.tcp
        parameters.allowLocalEndpointReuse = true

        connection = NWConnection(host: hostEndpoint, port: portEndpoint, using: parameters)

        // 添加超时控制
        var timeoutTask: DispatchWorkItem?

        // 设置连接状态变化的处理程序
        connection?.stateUpdateHandler = { [weak self] state in
            guard let self = self else { return }

            switch state {
            case .ready:
                print("已连接到服务器 \(self.host):\(self.port)")
                timeoutTask?.cancel()
                self.lastHeartbeatTime = Date() // 重置心跳时间
                self.currentReconnectAttempts = 0
                completion(true)
                self.receiveData()
            case .preparing:
                print("正在准备连接...")
            case .waiting(let error):
                print("等待连接: \(error.localizedDescription)")
            case .failed(let error):
                print("连接失败: \(error.localizedDescription)")
                timeoutTask?.cancel()
                self.handleConnectionFailure()
                completion(false)
            case .cancelled:
                print("连接已取消")
                timeoutTask?.cancel()
                completion(false)
            default:
                break
            }
        }

        // 启动连接
        connection?.start(queue: queue)

        // 设置超时
        timeoutTask = DispatchWorkItem { [weak self] in
            guard let self = self, self.connection?.state != .ready else { return }
            print("连接超时 (\(timeout)秒)")
            self.disconnect()
            completion(false)
        }

        if let timeoutTask = timeoutTask {
            queue.asyncAfter(deadline: .now() + timeout, execute: timeoutTask)
        }
    }

    private func handleConnectionFailure() {
        // 如果在后台且允许自动重连，启动重连
        if isInBackground && shouldAutoReconnect {
            startReconnectTimer()
        }
    }

    func disconnect() {
        shouldAutoReconnect = false
        stopReconnectTimer()
        stopHeartbeatCheck()
        stopBackgroundTask()

        connection?.cancel()
        connection = nil
        print("已断开连接")
    }

    func send(message: String, completion: @escaping (Bool) -> Void) {
        guard let connection = connection, let data = message.data(using: .utf8) else {
            completion(false)
            return
        }

        connection.send(content: data, completion: .contentProcessed { error in
            if let error = error {
                print("发送消息错误: \(error.localizedDescription)")
                completion(false)
            } else {
                print("消息已发送: \(message)")
                completion(true)
            }
        })
    }

    func send(data: Data, completion: @escaping (Bool) -> Void) {
        guard let connection = connection else {
            completion(false)
            return
        }

        connection.send(content: data, completion: .contentProcessed { error in
            if let error = error {
                print("发送数据错误: \(error.localizedDescription)")
                completion(false)
            } else {
                print("数据已发送: \(data.count) 字节")
                completion(true)
            }
        })
    }

    func onMessage(handler: @escaping (String) -> Void) {
        self.messageHandler = handler
    }

    func connectWithRetry(maxRetries: Int = 3, retryInterval: TimeInterval = 5.0, timeout: TimeInterval = 30.0, completion: @escaping (Bool) -> Void) {
        shouldAutoReconnect = true
        maxReconnectAttempts = maxRetries
        var retriesLeft = maxRetries

        func attemptConnect() {
            connect(timeout: timeout) { [weak self] success in
                guard let self = self else { return }

                if success {
                    print("连接成功")
                    completion(true)
                } else if retriesLeft > 0 {
                    retriesLeft -= 1
                    print("连接失败，将在 \(retryInterval) 秒后重试。剩余重试次数: \(retriesLeft)")

                    // 延迟后重试
                    self.queue.asyncAfter(deadline: .now() + retryInterval) {
                        attemptConnect()
                    }
                } else {
                    print("连接失败，已达到最大重试次数")
                    completion(false)
                }
            }
        }

        attemptConnect()
    }

    // MARK: - Enhanced Data Receiving with Heartbeat Support
    private func receiveData() {
        connection?.receive(minimumIncompleteLength: 1, maximumLength: 65536) { [weak self] (data, _, isComplete, error) in
            guard let self = self else { return }

            if let data = data, !data.isEmpty {
                if let message = String(data: data, encoding: .utf8) {
                    print("收到服务器消息: \(message)")

                    // 处理心跳消息
                    if message == "PING" {
                        // 响应心跳
                        self.send(message: "PONG") { _ in }
                        self.lastHeartbeatTime = Date()
                    } else if message == "PONG" || message == "HEARTBEAT" {
                        // 更新心跳时间
                        self.lastHeartbeatTime = Date()
                    } else {
                        // 普通消息，传递给处理器
                        self.messageHandler?(message)
                    }
                } else {
                    print("收到服务器二进制数据: \(data.count) 字节")
                }

                // 继续接收数据
                self.receiveData()
            } else if isComplete {
                print("服务器已关闭连接")
                self.handleConnectionFailure()
            } else if let error = error {
                print("接收数据错误: \(error.localizedDescription)")
                self.handleConnectionFailure()
            }
        }
    }

    // MARK: - Configuration Methods
    func setAutoReconnect(_ enabled: Bool) {
        shouldAutoReconnect = enabled
    }

    func setMaxReconnectAttempts(_ attempts: Int) {
        maxReconnectAttempts = attempts
    }

    // MARK: - Status Methods
    var isConnected: Bool {
        return connection?.state == .ready
    }
}
